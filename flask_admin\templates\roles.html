{% extends "base.html" %}

{% block title %}Roles - SSO Admin Panel{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-shield-lock"></i> Roles</h1>
    {% if not appadmin_readonly %}
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('create_role') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> New Role
            </a>
        </div>
    </div>
    {% endif %}
</div>

<div class="table-responsive">
    <table class="table table-striped align-middle">
        <thead>
            <tr>
                <th>ID</th>
                <th>Name</th>
                <th>Permissions</th>
                {% if not appadmin_readonly %}<th width="120">Actions</th>{% endif %}
            </tr>
        </thead>
        <tbody>
            {% for role in roles %}
            <tr>
                <td>{{ role.id }}</td>
                <td>{{ role.name }}</td>
                <td>
                    {% if role.permissions %}
                        <ul class="mb-0 ps-3">
                        {% for perm in role.permissions %}
    <li>
        <span data-bs-toggle="tooltip" data-bs-placement="top" title="{{ permissions_desc[perm] }}">
            {{ permissions_dict[perm] if permissions_dict[perm] else perm }}
        </span>
    </li>
{% endfor %}
                        </ul>
                    {% else %}
                        <span class="text-muted">None</span>
                    {% endif %}
                </td>
                {% if not appadmin_readonly %}
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <a href="{{ url_for('edit_role', role_id=role.id) }}" class="btn btn-outline-primary" title="Edit">
                            <i class="bi bi-pencil"></i>
                        </a>
                        <!-- Delete Button triggers Modal -->
<button type="button" class="btn btn-outline-danger" title="Delete" data-bs-toggle="modal" data-bs-target="#deleteRoleModal{{ role.id }}">
    <i class="bi bi-trash"></i>
</button>
<!-- Modal -->
<div class="modal fade" id="deleteRoleModal{{ role.id }}" tabindex="-1" aria-labelledby="deleteRoleModalLabel{{ role.id }}" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deleteRoleModalLabel{{ role.id }}">Confirm Delete</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        Are you sure you want to delete the role <strong>{{ role.name }}</strong>?
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <form method="POST" action="{{ url_for('delete_role', role_id=role.id) }}" class="d-inline m-0 p-0">
          <button type="submit" class="btn btn-danger">Delete</button>
        </form>
      </div>
    </div>
  </div>
</div>
                    </div>
                </td>
                {% endif %}
            </tr>
            {% else %}
            <tr><td colspan="3" class="text-center text-muted">No roles found.</td></tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.forEach(function (tooltipTriggerEl) {
            new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
