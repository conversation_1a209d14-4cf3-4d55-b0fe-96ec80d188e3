#!/usr/bin/env python3
"""
Flask Admin Frontend for SSO System

This Flask application provides a web-based admin interface that consumes
the FastAPI backend endpoints to manage users, applications, and system monitoring.
"""

import os
import requests
from datetime import datetime
from functools import wraps
from typing import Optional, Dict, Any

from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, TextAreaField, SelectField
from wtforms.validators import DataRequired, Email, Length, Optional as OptionalValidator
from wtforms.widgets import TextArea

# --- Static Sample Data for Demo Purposes ---

BRANCHES = [
    {'id': 1, 'name': 'Central'},
    {'id': 2, 'name': 'North'},
    {'id': 3, 'name': 'South'},
    {'id': 4, 'name': 'East'},
    {'id': 5, 'name': 'West'},
]

DEPARTMENTS = [
    {'id': 1, 'name': 'Operations'},
    {'id': 2, 'name': 'Finance'},
    {'id': 3, 'name': 'HR'},
    {'id': 4, 'name': 'IT'},
]

POSITIONS = [
    {'id': 1, 'name': 'Branch Manager', 'department_id': 1},
    {'id': 2, 'name': 'Loan Officer', 'department_id': 1},
    {'id': 3, 'name': 'Accountant', 'department_id': 2},
    {'id': 4, 'name': 'HR Specialist', 'department_id': 3},
    {'id': 5, 'name': 'IT Support', 'department_id': 4},
]

ROLES = [
    {'id': 1, 'name': 'Admin', 'permissions': ['view_users', 'edit_users', 'delete_users', 'manage_roles']},
    {'id': 2, 'name': 'Branch Manager', 'permissions': ['view_users', 'edit_users']},
    {'id': 3, 'name': 'Employee', 'permissions': ['view_users']},
    # New Application Admin role
    {'id': 4, 'name': 'Application Admin', 'permissions': ['view_users', 'edit_users']},
]

PERMISSIONS = [
    {'code': 'view_users', 'label': 'View Users', 'description': 'Allows viewing user details and lists.'},
    {'code': 'edit_users', 'label': 'Edit Users', 'description': 'Allows editing user information and roles.'},
    {'code': 'delete_users', 'label': 'Delete Users', 'description': 'Allows deletion of users from the system.'},
    {'code': 'manage_roles', 'label': 'Manage Roles', 'description': 'Allows creation, editing, and deletion of roles.'},
    {'code': 'view_applications', 'label': 'View Applications', 'description': 'Allows viewing application details.'},
    {'code': 'edit_applications', 'label': 'Edit Applications', 'description': 'Allows editing application settings.'},
]

EMPLOYEES = [
    {'id': 1, 'employee_id': 'EMP001', 'full_name': 'Alice Smith', 'branch_id': 1, 'position_id': 1, 'manager_id': None},
    {'id': 2, 'employee_id': 'EMP002', 'full_name': 'Bob Johnson', 'branch_id': 2, 'position_id': 2, 'manager_id': 1},
    {'id': 3, 'employee_id': 'EMP003', 'full_name': 'Carol Lee', 'branch_id': 3, 'position_id': 3, 'manager_id': 1},
    {'id': 4, 'employee_id': 'EMP004', 'full_name': 'David Kim', 'branch_id': 4, 'position_id': 4, 'manager_id': 1},
    {'id': 5, 'employee_id': 'EMP005', 'full_name': 'Eva Brown', 'branch_id': 5, 'position_id': 5, 'manager_id': 1},
]

USERS = [
    {'id': 1, 'username': 'alice', 'email': '<EMAIL>', 'employee_id': 'EMP001', 'role_id': 1, 'is_active': True, 'is_verified': True, 'last_login': '2025-07-01T09:00:00', 'created_at': '2025-06-01T10:00:00'},
    {'id': 2, 'username': 'bob', 'email': '<EMAIL>', 'employee_id': 'EMP002', 'role_id': 2, 'is_active': True, 'is_verified': True, 'last_login': '2025-07-02T09:00:00', 'created_at': '2025-06-02T10:00:00'},
    {'id': 3, 'username': 'carol', 'email': '<EMAIL>', 'employee_id': 'EMP003', 'role_id': 3, 'is_active': True, 'is_verified': False, 'last_login': '2025-07-03T09:00:00', 'created_at': '2025-06-03T10:00:00'},
    {'id': 4, 'username': 'david', 'email': '<EMAIL>', 'employee_id': 'EMP004', 'role_id': 3, 'is_active': False, 'is_verified': False, 'last_login': '2025-07-04T09:00:00', 'created_at': '2025-06-04T10:00:00'},
    {'id': 5, 'username': 'eva', 'email': '<EMAIL>', 'employee_id': 'EMP005', 'role_id': 3, 'is_active': True, 'is_verified': True, 'last_login': '2025-07-05T09:00:00', 'created_at': '2025-06-05T10:00:00'},
    # Demo Application Admin user
    {'id': 6, 'username': 'appadmin', 'email': '<EMAIL>', 'employee_id': 'EMP001', 'role_id': 4, 'is_active': True, 'is_verified': True, 'last_login': None, 'created_at': '2025-07-10T10:00:00'},
]

# Configuration
class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    FASTAPI_BASE_URL = os.environ.get('FASTAPI_BASE_URL') or 'http://localhost:8000'
    WTF_CSRF_ENABLED = True

# Flask app initialization
app = Flask(__name__)
app.config.from_object(Config)

# API Client for FastAPI communication
class FastAPIClient:
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def set_auth_token(self, token: str):
        """Set JWT token for authenticated requests"""
        self.session.headers.update({'Authorization': f'Bearer {token}'})
    
    def clear_auth_token(self):
        """Clear JWT token"""
        if 'Authorization' in self.session.headers:
            del self.session.headers['Authorization']
    
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """Authenticate with FastAPI backend"""
        try:
            response = self.session.post(
                f'{self.base_url}/auth/login',
                data={'username': username, 'password': password},
                headers={
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'accept': 'application/json'
                }
            )
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': 'Invalid credentials'}
        except Exception as e:
            return {'error': f'Connection error: {str(e)}'}
    
    def get(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make GET request to FastAPI"""
        try:
            response = self.session.get(f'{self.base_url}{endpoint}', params=params)
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 401:
                return {'error': 'Unauthorized', 'status_code': 401}
            elif response.status_code == 403:
                return {'error': 'Forbidden - Admin access required', 'status_code': 403}
            else:
                return {'error': f'API Error: {response.status_code}', 'status_code': response.status_code}
        except Exception as e:
            return {'error': f'Connection error: {str(e)}'}
    
    def post(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Make POST request to FastAPI"""
        try:
            response = self.session.post(
                f'{self.base_url}{endpoint}',
                json=data,
                headers={'Content-Type': 'application/json'}
            )
            if response.status_code in [200, 201]:
                return response.json()
            elif response.status_code == 401:
                return {'error': 'Unauthorized', 'status_code': 401}
            elif response.status_code == 403:
                return {'error': 'Forbidden - Admin access required', 'status_code': 403}
            else:
                return {'error': f'API Error: {response.status_code}', 'status_code': response.status_code}
        except Exception as e:
            return {'error': f'Connection error: {str(e)}'}
    
    def put(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Make PUT request to FastAPI"""
        try:
            response = self.session.put(
                f'{self.base_url}{endpoint}',
                json=data,
                headers={'Content-Type': 'application/json'}
            )
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 401:
                return {'error': 'Unauthorized', 'status_code': 401}
            elif response.status_code == 403:
                return {'error': 'Forbidden - Admin access required', 'status_code': 403}
            else:
                return {'error': f'API Error: {response.status_code}', 'status_code': response.status_code}
        except Exception as e:
            return {'error': f'Connection error: {str(e)}'}
    
    def delete(self, endpoint: str) -> Dict[str, Any]:
        """Make DELETE request to FastAPI"""
        try:
            response = self.session.delete(f'{self.base_url}{endpoint}')
            if response.status_code in [200, 204]:
                return {'success': True}
            elif response.status_code == 401:
                return {'error': 'Unauthorized', 'status_code': 401}
            elif response.status_code == 403:
                return {'error': 'Forbidden - Admin access required', 'status_code': 403}
            else:
                return {'error': f'API Error: {response.status_code}', 'status_code': response.status_code}
        except Exception as e:
            return {'error': f'Connection error: {str(e)}'}

# Global API client
api_client = FastAPIClient(Config.FASTAPI_BASE_URL)

# Helper function for consistent API error handling
def handle_api_error(result: Dict[str, Any], error_message: str = "API Error") -> bool:
    """
    Handle API errors consistently across the application.
    Returns True if there was an error, False otherwise.
    """
    if 'error' in result:
        if result.get('status_code') in [401, 403]:
            flash('Session expired. Please log in again.', 'warning')
            return True
        elif result.get('status_code') == 404:
            flash('Resource not found.', 'danger')
            return True
        elif result.get('status_code') == 422:
            # Validation error
            error_detail = result.get('error', 'Validation error')
            flash(f"Validation error: {error_detail}", 'danger')
            return True
        else:
            flash(f"{error_message}: {result['error']}", 'danger')
            return True
    return False

# Authentication decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'access_token' not in session:
            return redirect(url_for('login', next=request.url))

        # Set token for the API client for this request
        api_client.set_auth_token(session['access_token'])

        # Ensure user is an admin
        if not session.get('user', {}).get('is_superuser'):
            flash('Admin access required.', 'danger')
            return redirect(url_for('login'))

        return f(*args, **kwargs)
    return decorated_function

def super_admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = session.get('user', {})
        if not user.get('is_superuser'):
            flash('Super Admin access required.', 'danger')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function


def app_admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = session.get('user', {})
        # Allow superusers to access app admin features
        if user.get('is_superuser'):
            return f(*args, **kwargs)
        if 'app_admin' not in session.get('roles', []):
            flash('Application Admin access required.', 'danger')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

# Forms
class LoginForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=3, max=50)])
    password = PasswordField('Password', validators=[DataRequired()])

class UserForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=3, max=50)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    full_name = StringField('Full Name', validators=[OptionalValidator(), Length(max=255)])
    password = PasswordField('Password', validators=[OptionalValidator(), Length(min=6)])
    is_active = BooleanField('Active')
    is_verified = BooleanField('Verified')
    is_superuser = BooleanField('Admin')

class ApplicationForm(FlaskForm):
    name = StringField('Application Name', validators=[DataRequired(), Length(min=3, max=100)])
    description = TextAreaField('Description', validators=[OptionalValidator()])
    redirect_uris = TextAreaField('Redirect URIs (one per line)', validators=[DataRequired()])
    is_active = BooleanField('Active')

# Routes


@app.route('/permissions')
@login_required
def permissions():
    """List all permissions via FastAPI backend"""
    permissions_result = api_client.get('/api/admin/permissions')

    if 'error' in permissions_result:
        flash(f"Error loading permissions: {permissions_result['error']}", 'danger')
        if permissions_result.get('status_code') in [401, 403]:
            return redirect(url_for('logout'))
        permissions_data = []
    else:
        permissions_data = permissions_result

    return render_template('permissions.html', permissions=permissions_data)

@app.route('/permissions/create', methods=['GET', 'POST'])
@super_admin_required
def create_permission():
    class PermissionForm(FlaskForm):
        code = StringField('Permission Code', validators=[DataRequired(), Length(min=3, max=50)])
        label = StringField('Label', validators=[DataRequired(), Length(min=3, max=100)])
    form = PermissionForm()
    if form.validate_on_submit():
        if any(p['code'] == form.code.data for p in PERMISSIONS):
            flash('Permission code already exists!', 'error')
        else:
            PERMISSIONS.append({'code': form.code.data, 'label': form.label.data})
            flash('Permission created (static, not persisted)', 'success')
            return redirect(url_for('permissions'))
    return render_template('permission_form.html', form=form, title='Create Permission')

@app.route('/permissions/<code>/edit', methods=['GET', 'POST'])
@super_admin_required
def edit_permission(code):
    perm = next((p for p in PERMISSIONS if p['code'] == code), None)
    if not perm:
        flash('Permission not found', 'error')
        return redirect(url_for('permissions'))
    class PermissionForm(FlaskForm):
        code = StringField('Permission Code', validators=[DataRequired(), Length(min=3, max=50)])
        label = StringField('Label', validators=[DataRequired(), Length(min=3, max=100)])
    form = PermissionForm(obj=perm)
    if form.validate_on_submit():
        if any(p['code'] == form.code.data and p['code'] != code for p in PERMISSIONS):
            flash('Permission code already exists!', 'error')
        else:
            perm['code'] = form.code.data
            perm['label'] = form.label.data
            flash('Permission updated (static, not persisted)', 'success')
            return redirect(url_for('permissions'))
    return render_template('permission_form.html', form=form, title='Edit Permission')

@app.route('/permissions/<code>/delete', methods=['POST'])
@super_admin_required
def delete_permission(code):
    perm = next((p for p in PERMISSIONS if p['code'] == code), None)
    if perm:
        PERMISSIONS.remove(perm)
        flash('Permission deleted (static, not persisted)', 'success')
    else:
        flash('Permission not found', 'error')
    return redirect(url_for('permissions'))


@app.route('/roles')
@login_required
def roles():
    """List all roles via FastAPI backend"""
    roles_result = api_client.get('/api/admin/roles')
    permissions_result = api_client.get('/api/admin/permissions')

    if 'error' in roles_result:
        flash(f"Error loading roles: {roles_result['error']}", 'danger')
        if roles_result.get('status_code') in [401, 403]:
            return redirect(url_for('logout'))
        roles_data = []
    else:
        roles_data = roles_result

    if 'error' in permissions_result:
        flash(f"Error loading permissions: {permissions_result['error']}", 'danger')
        permissions_data = []
    else:
        permissions_data = permissions_result

    # Create permission dictionaries for template
    permissions_dict = {p['permission_name']: p['permission_name'] for p in permissions_data}
    permissions_desc = {p['permission_name']: p.get('description', '') for p in permissions_data}

    return render_template('roles.html', roles=roles_data, permissions_dict=permissions_dict, permissions_desc=permissions_desc)

@app.route('/roles/create', methods=['GET', 'POST'])
@super_admin_required
def create_role():
    class RoleForm(FlaskForm):
        name = StringField('Role Name', validators=[DataRequired(), Length(min=3, max=50)])
        permissions = SelectField('Permissions', choices=[(p['code'], p['label']) for p in PERMISSIONS], coerce=str, render_kw={'multiple': True})
    form = RoleForm()
    form.permissions.choices = [(p['code'], p['label']) for p in PERMISSIONS]
    if form.validate_on_submit():
        if any(r['name'].lower() == form.name.data.lower() for r in ROLES):
            flash('Role name already exists!', 'error')
        else:
            new_id = max(r['id'] for r in ROLES) + 1 if ROLES else 1
            ROLES.append({'id': new_id, 'name': form.name.data, 'permissions': request.form.getlist('permissions')})
            flash('Role created (static, not persisted)', 'success')
            return redirect(url_for('roles'))
    return render_template('role_form.html', form=form, title='Create Role', permissions=PERMISSIONS)

@app.route('/roles/<int:role_id>/edit', methods=['GET', 'POST'])
@super_admin_required
def edit_role(role_id):
    role = next((r for r in ROLES if r['id'] == role_id), None)
    if not role:
        flash('Role not found', 'error')
        return redirect(url_for('roles'))
    class RoleForm(FlaskForm):
        name = StringField('Role Name', validators=[DataRequired(), Length(min=3, max=50)])
        permissions = SelectField('Permissions', choices=[(p['code'], p['label']) for p in PERMISSIONS], coerce=str, render_kw={'multiple': True})
    form = RoleForm(obj=role)
    form.permissions.choices = [(p['code'], p['label']) for p in PERMISSIONS]
    form.permissions.data = role.get('permissions', [])
    if form.validate_on_submit():
        if any(r['name'].lower() == form.name.data.lower() and r['id'] != role_id for r in ROLES):
            flash('Role name already exists!', 'error')
        else:
            role['name'] = form.name.data
            role['permissions'] = request.form.getlist('permissions')
            flash('Role updated (static, not persisted)', 'success')
            return redirect(url_for('roles'))
    return render_template('role_form.html', form=form, title='Edit Role', permissions=PERMISSIONS, role=role)

@app.route('/roles/<int:role_id>/delete', methods=['POST'])
@super_admin_required
def delete_role(role_id):
    role = next((r for r in ROLES if r['id'] == role_id), None)
    if role:
        ROLES.remove(role)
        flash('Role deleted (static, not persisted)', 'success')
    else:
        flash('Role not found', 'error')
    return redirect(url_for('roles'))


@app.route('/me', methods=['GET', 'POST'])
@login_required
def me():
    """Profile page for the current user (static data)"""
    # For static demo, use session['user']['username'] if present, else default to 'alice'
    username = session.get('user', {}).get('username')
    user = next((u for u in USERS if u['username'] == username), None)
    if not user:
        user = USERS[0]  # fallback to first static user
    emp = next((e for e in EMPLOYEES if e['employee_id'] == user['employee_id']), None) if user else None
    if not user or not emp:
        flash('User not found', 'error')
        return redirect(url_for('users'))
    class StaticProfileForm(UserForm):
        employee_id = StringField('Employee ID', default=emp['employee_id'], render_kw={'readonly': True})
        branch_id = SelectField('Branch', choices=[(str(b['id']), b['name']) for b in BRANCHES], default=str(emp['branch_id']), render_kw={'disabled': True})
        position_id = SelectField('Position', choices=[(str(p['id']), p['name']) for p in POSITIONS], default=str(emp['position_id']), render_kw={'disabled': True})
        role_id = SelectField('Role', choices=[(str(r['id']), r['name']) for r in ROLES], default=str(user['role_id']), render_kw={'disabled': True})
        manager_id = SelectField('Manager', choices=[('', 'None')] + [(str(e['id']), e['full_name']) for e in EMPLOYEES], default=str(emp['manager_id']) if emp['manager_id'] else '', render_kw={'disabled': True})
    form = StaticProfileForm(obj=user)
    form.full_name.data = emp['full_name']
    if form.validate_on_submit():
        # Only allow updating username, email, full_name, password
        if any(u['username'] == form.username.data and u['id'] != user['id'] for u in USERS):
            flash('Username already exists!', 'error')
        else:
            user['username'] = form.username.data
            user['email'] = form.email.data
            emp['full_name'] = form.full_name.data
            if form.password.data:
                user['password'] = form.password.data
            flash('Profile updated (static, not persisted)', 'success')
            return redirect(url_for('me'))
    # Show effective permissions in profile
    role = next((r for r in ROLES if r['id'] == user['role_id']), None)
    permissions_dict = {p['code']: p['label'] for p in PERMISSIONS}
    effective_permissions = [permissions_dict.get(p, p) for p in (role['permissions'] if role else [])]
    return render_template('user_form.html', form=form, title='My Profile', user=user, profile_page=True, effective_permissions=effective_permissions)

@app.route('/')
def index():
    """Redirect to admin dashboard"""
    if 'access_token' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Admin login page"""
    if 'access_token' in session and session.get('user', {}).get('is_superuser'):
        return redirect(url_for('dashboard'))

    form = LoginForm()
    if form.validate_on_submit():
        # Try to authenticate with FastAPI backend
        result = api_client.login(form.username.data, form.password.data)

        if 'access_token' in result:
            # Successful authentication with FastAPI
            user_data = result.get('user', {})

            # Check if user has admin privileges
            if user_data.get('is_superuser'):
                session['access_token'] = result['access_token']
                session['user'] = user_data
                api_client.set_auth_token(result['access_token'])
                flash('Login successful!', 'success')
                return redirect(url_for('dashboard'))
            else:
                flash('You do not have admin privileges.', 'danger')
                return redirect(url_for('login'))
        else:
            # Authentication failed
            error_message = result.get('error', 'Invalid credentials or connection issue.')
            flash(error_message, 'danger')

    return render_template('login.html', form=form, title='Admin Login')

@app.route('/logout')
def logout():
    """Admin logout"""
    session.pop('access_token', None)
    session.pop('user', None)
    api_client.clear_auth_token()
    flash('You have been logged out.', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    """Admin dashboard with comprehensive data"""
    # Get system statistics
    stats = api_client.get('/api/admin/stats')

    # Get recent activities
    activities = api_client.get('/api/admin/activities', params={'limit': 10})

    # Get recent users
    recent_users = api_client.get('/api/admin/users/search', params={'page': 1, 'limit': 5})

    if 'error' in stats:
        flash(f"Error loading dashboard: {stats['error']}", 'danger')
        if stats.get('status_code') in [401, 403]:
            return redirect(url_for('logout'))
        stats = {}

    if 'error' in activities:
        activities = {'activities': []}

    if 'error' in recent_users:
        recent_users = {'users': []}

    dashboard_data = {
        'stats': stats,
        'activities': activities.get('activities', []),
        'recent_users': recent_users.get('users', []),
        'user': session.get('user')
    }

    return render_template('dashboard.html', **dashboard_data)

# Apply super_admin_required to SSO Admin routes
@app.route('/users/create', methods=['GET', 'POST'])
@super_admin_required
def create_user():
    """Create a new user via FastAPI backend"""
    class CreateUserForm(FlaskForm):
        username = StringField('Username', validators=[DataRequired(), Length(min=3, max=50)])
        email = StringField('Email', validators=[DataRequired(), Email()])
        full_name = StringField('Full Name', validators=[OptionalValidator(), Length(max=255)])
        password = PasswordField('Password', validators=[DataRequired(), Length(min=8)])
        is_active = BooleanField('Active', default=True)
        is_verified = BooleanField('Verified', default=False)
        is_superuser = BooleanField('Superuser', default=False)

    form = CreateUserForm()

    if form.validate_on_submit():
        # Prepare user data for API
        user_data = {
            'username': form.username.data,
            'email': form.email.data,
            'full_name': form.full_name.data,
            'password': form.password.data,
            'is_active': form.is_active.data,
            'is_verified': form.is_verified.data,
            'is_superuser': form.is_superuser.data
        }

        # Call FastAPI to create user
        result = api_client.post('/api/admin/users', user_data)

        if 'error' in result:
            flash(f"Error creating user: {result['error']}", 'danger')
            if result.get('status_code') in [401, 403]:
                return redirect(url_for('logout'))
        else:
            flash('User created successfully!', 'success')
            return redirect(url_for('users'))

    return render_template('user_form.html', form=form, title='Create User', user=None)

@app.route('/users', methods=['GET'])
@login_required
def users():
    """User management page with search and filter functionality"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    is_active = request.args.get('is_active')
    page_size = 20

    # Call FastAPI backend to get users
    params = {
        'page': page,
        'limit': page_size
    }
    if search:
        params['q'] = search

    result = api_client.get('/api/admin/users/search', params=params)

    if 'error' in result:
        flash(f"Error loading users: {result['error']}", 'danger')
        if result.get('status_code') in [401, 403]:
            return redirect(url_for('logout'))
        # Fallback to empty data
        result = {'users': [], 'total': 0, 'page': 1, 'limit': page_size}

    # Process users data for display
    users_data = result.get('users', [])
    for user in users_data:
        # Add display-friendly fields
        user['status_badge'] = 'success' if user.get('is_active') else 'danger'
        user['verification_badge'] = 'success' if user.get('is_verified') else 'warning'
        user['admin_badge'] = 'info' if user.get('is_superuser') else 'secondary'

        # Format dates
        if user.get('created_at'):
            try:
                created_dt = datetime.fromisoformat(user['created_at'].replace('Z', '+00:00'))
                user['created_at_formatted'] = created_dt.strftime('%Y-%m-%d %H:%M')
            except:
                user['created_at_formatted'] = user['created_at']

        if user.get('last_login'):
            try:
                login_dt = datetime.fromisoformat(user['last_login'].replace('Z', '+00:00'))
                user['last_login_formatted'] = login_dt.strftime('%Y-%m-%d %H:%M')
            except:
                user['last_login_formatted'] = user['last_login']
        else:
            user['last_login_formatted'] = 'Never'

    total = result.get('total', 0)
    pages = (total + page_size - 1) // page_size if total > 0 else 1

    data = {
        'users': users_data,
        'total': total,
        'page': page,
        'pages': pages,
    }
    
    return render_template(
        'users.html',
        data=data,
        search=search if search else '',
        is_active=is_active if is_active else '',
    )


@app.route('/users/<user_id>/edit', methods=['GET', 'POST'])
@super_admin_required
def edit_user(user_id):
    """Edit user via FastAPI backend"""
    # Get user details from API
    user_result = api_client.get(f'/api/admin/users/{user_id}')

    if 'error' in user_result:
        flash(f"Error loading user: {user_result['error']}", 'danger')
        if user_result.get('status_code') in [401, 403]:
            return redirect(url_for('logout'))
        return redirect(url_for('users'))

    user_data = user_result

    class EditUserForm(FlaskForm):
        username = StringField('Username', validators=[DataRequired(), Length(min=3, max=50)])
        email = StringField('Email', validators=[DataRequired(), Email()])
        full_name = StringField('Full Name', validators=[OptionalValidator(), Length(max=255)])
        password = PasswordField('Password (leave blank to keep current)', validators=[OptionalValidator(), Length(min=8)])
        is_active = BooleanField('Active')
        is_verified = BooleanField('Verified')
        is_superuser = BooleanField('Superuser')

    # Pre-populate form with current user data
    form = EditUserForm(
        username=user_data.get('username'),
        email=user_data.get('email'),
        full_name=user_data.get('full_name'),
        is_active=user_data.get('is_active', True),
        is_verified=user_data.get('is_verified', False),
        is_superuser=user_data.get('is_superuser', False)
    )

    if form.validate_on_submit():
        # Prepare update data
        update_data = {
            'username': form.username.data,
            'email': form.email.data,
            'full_name': form.full_name.data,
            'is_active': form.is_active.data,
            'is_verified': form.is_verified.data,
            'is_superuser': form.is_superuser.data
        }

        # Only include password if provided
        if form.password.data:
            update_data['password'] = form.password.data

        # Call API to update user
        result = api_client.put(f'/api/admin/users/{user_id}', update_data)

        if 'error' in result:
            flash(f"Error updating user: {result['error']}", 'danger')
            if result.get('status_code') in [401, 403]:
                return redirect(url_for('logout'))
        else:
            flash('User updated successfully!', 'success')
            return redirect(url_for('users'))

    return render_template('user_form.html', form=form, title='Edit User', user=user_data)





@app.route('/users/delete/<user_id>', methods=['POST'])
@super_admin_required
def delete_user(user_id):
    """Delete a user via FastAPI backend"""
    result = api_client.delete(f'/api/admin/users/{user_id}')

    if 'error' in result:
        flash(f"Error deleting user: {result['error']}", 'danger')
        if result.get('status_code') in [401, 403]:
            return redirect(url_for('logout'))
    else:
        flash('User deleted successfully!', 'success')

    return redirect(url_for('users'))




@app.route('/applications')
@super_admin_required
def applications():
    """Application management page"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    params = {'page': page, 'size': 20}
    if search:
        params['search'] = search
    
    apps_data = api_client.get('/api/admin/applications', params=params)
    
    if 'error' in apps_data:
        flash(f"Error loading applications: {apps_data['error']}", 'error')
        apps_data = {'applications': [], 'total': 0, 'page': 1, 'pages': 1}
    
    return render_template('applications.html', data=apps_data, search=search)

# Health check endpoint
@app.route('/health')
def health_check():
    """Health check endpoint for Docker"""
    return {'status': 'healthy', 'service': 'flask-admin'}, 200

# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

# Template filters
@app.template_filter('datetime')
def datetime_filter(value):
    """Format datetime for display"""
    if value:
        try:
            if isinstance(value, str):
                dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
            else:
                dt = value
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return value
    return 'Never'

@app.template_filter('status_badge')
def status_badge_filter(is_active):
    """Generate status badge HTML"""
    if is_active:
        return '<span class="badge bg-success">Active</span>'
    else:
        return '<span class="badge bg-danger">Inactive</span>'

# Application Admin: View users (read-only)
@app.route('/appadmin/users')
@app_admin_required
def appadmin_users():
    # Reuse enrich_user from users() route
    def enrich_user(user):
        emp = next((e for e in EMPLOYEES if e['employee_id'] == user['employee_id']), None)
        role = next((r for r in ROLES if r['id'] == user['role_id']), None)
        branch = next((b for b in BRANCHES if b['id'] == emp['branch_id']), None) if emp else None
        pos = next((p for p in POSITIONS if p['id'] == emp['position_id']), None) if emp else None
        dept = next((d for d in DEPARTMENTS if d['id'] == pos['department_id']), None) if pos else None
        manager = next((e for e in EMPLOYEES if emp and emp['manager_id'] == e['id']), None)
        permissions_dict = {p['code']: p['label'] for p in PERMISSIONS}
        effective_permissions = [permissions_dict.get(p, p) for p in (role['permissions'] if role else [])]
        return {
            **user,
            'full_name': emp['full_name'] if emp else '',
            'branch': branch['name'] if branch else '',
            'position': pos['name'] if pos else '',
            'department': dept['name'] if dept else '',
            'manager_name': manager['full_name'] if manager else '',
            'role': role['name'] if role else '',
            'effective_permissions': effective_permissions,
        }
    data = {'users': [enrich_user(u) for u in USERS]}
    return render_template('users.html', data=data, appadmin_readonly=True)

# Application Admin: View roles (read-only)
@app.route('/appadmin/roles')
@app_admin_required
def appadmin_roles():
    permissions_dict = {p['code']: p['label'] for p in PERMISSIONS}
    permissions_desc = {p['code']: p.get('description', '') for p in PERMISSIONS}
    return render_template('roles.html', roles=ROLES, permissions_dict=permissions_dict, permissions_desc=permissions_desc, appadmin_readonly=True)

# Application Admin: Assign role to user (POST)
@app.route('/appadmin/assign-role', methods=['POST'])
@app_admin_required
def appadmin_assign_role():
    user_id = request.form.get('user_id', type=int)
    role_id = request.form.get('role_id', type=int)
    user = next((u for u in USERS if u['id'] == user_id), None)
    role = next((r for r in ROLES if r['id'] == role_id), None)
    if not user or not role:
        flash('User or role not found.', 'danger')
        return redirect(url_for('appadmin_users'))
    user['role_id'] = role_id
    flash(f"Role '{role['name']}' assigned to user '{user['username']}' (static, not persisted)", 'success')
    return redirect(url_for('appadmin_users'))

if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='Flask Admin Panel for SSO System')
    parser.add_argument('--port', type=int, default=5000, help='Port to run the application on')
    args = parser.parse_args()

    app.run(debug=True, host='0.0.0.0', port=args.port)