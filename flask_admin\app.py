#!/usr/bin/env python3
"""
Flask Admin Frontend for SSO System

This Flask application provides a web-based admin interface that consumes
the FastAPI backend endpoints to manage users, applications, and system monitoring.
"""

import os
import requests
from datetime import datetime
from functools import wraps
from typing import Optional, Dict, Any

from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from flask_wtf import FlaskForm
from wtforms import StringField, PasswordField, BooleanField, TextAreaField, SelectField
from wtforms.validators import DataRequired, Email, Length, Optional as OptionalValidator
from wtforms.widgets import TextArea

# --- Static Sample Data for Demo Purposes ---

BRANCHES = [
    {'id': 1, 'name': 'Central'},
    {'id': 2, 'name': 'North'},
    {'id': 3, 'name': 'South'},
    {'id': 4, 'name': 'East'},
    {'id': 5, 'name': 'West'},
]

DEPARTMENTS = [
    {'id': 1, 'name': 'Operations'},
    {'id': 2, 'name': 'Finance'},
    {'id': 3, 'name': 'HR'},
    {'id': 4, 'name': 'IT'},
]

POSITIONS = [
    {'id': 1, 'name': 'Branch Manager', 'department_id': 1},
    {'id': 2, 'name': 'Loan Officer', 'department_id': 1},
    {'id': 3, 'name': 'Accountant', 'department_id': 2},
    {'id': 4, 'name': 'HR Specialist', 'department_id': 3},
    {'id': 5, 'name': 'IT Support', 'department_id': 4},
]

ROLES = [
    {'id': 1, 'name': 'Admin', 'permissions': ['view_users', 'edit_users', 'delete_users', 'manage_roles']},
    {'id': 2, 'name': 'Branch Manager', 'permissions': ['view_users', 'edit_users']},
    {'id': 3, 'name': 'Employee', 'permissions': ['view_users']},
    # New Application Admin role
    {'id': 4, 'name': 'Application Admin', 'permissions': ['view_users', 'edit_users']},
]

PERMISSIONS = [
    {'code': 'view_users', 'label': 'View Users', 'description': 'Allows viewing user details and lists.'},
    {'code': 'edit_users', 'label': 'Edit Users', 'description': 'Allows editing user information and roles.'},
    {'code': 'delete_users', 'label': 'Delete Users', 'description': 'Allows deletion of users from the system.'},
    {'code': 'manage_roles', 'label': 'Manage Roles', 'description': 'Allows creation, editing, and deletion of roles.'},
    {'code': 'view_applications', 'label': 'View Applications', 'description': 'Allows viewing application details.'},
    {'code': 'edit_applications', 'label': 'Edit Applications', 'description': 'Allows editing application settings.'},
]

EMPLOYEES = [
    {'id': 1, 'employee_id': 'EMP001', 'full_name': 'Alice Smith', 'branch_id': 1, 'position_id': 1, 'manager_id': None},
    {'id': 2, 'employee_id': 'EMP002', 'full_name': 'Bob Johnson', 'branch_id': 2, 'position_id': 2, 'manager_id': 1},
    {'id': 3, 'employee_id': 'EMP003', 'full_name': 'Carol Lee', 'branch_id': 3, 'position_id': 3, 'manager_id': 1},
    {'id': 4, 'employee_id': 'EMP004', 'full_name': 'David Kim', 'branch_id': 4, 'position_id': 4, 'manager_id': 1},
    {'id': 5, 'employee_id': 'EMP005', 'full_name': 'Eva Brown', 'branch_id': 5, 'position_id': 5, 'manager_id': 1},
]

USERS = [
    {'id': 1, 'username': 'alice', 'email': '<EMAIL>', 'employee_id': 'EMP001', 'role_id': 1, 'is_active': True, 'is_verified': True, 'last_login': '2025-07-01T09:00:00', 'created_at': '2025-06-01T10:00:00'},
    {'id': 2, 'username': 'bob', 'email': '<EMAIL>', 'employee_id': 'EMP002', 'role_id': 2, 'is_active': True, 'is_verified': True, 'last_login': '2025-07-02T09:00:00', 'created_at': '2025-06-02T10:00:00'},
    {'id': 3, 'username': 'carol', 'email': '<EMAIL>', 'employee_id': 'EMP003', 'role_id': 3, 'is_active': True, 'is_verified': False, 'last_login': '2025-07-03T09:00:00', 'created_at': '2025-06-03T10:00:00'},
    {'id': 4, 'username': 'david', 'email': '<EMAIL>', 'employee_id': 'EMP004', 'role_id': 3, 'is_active': False, 'is_verified': False, 'last_login': '2025-07-04T09:00:00', 'created_at': '2025-06-04T10:00:00'},
    {'id': 5, 'username': 'eva', 'email': '<EMAIL>', 'employee_id': 'EMP005', 'role_id': 3, 'is_active': True, 'is_verified': True, 'last_login': '2025-07-05T09:00:00', 'created_at': '2025-06-05T10:00:00'},
    # Demo Application Admin user
    {'id': 6, 'username': 'appadmin', 'email': '<EMAIL>', 'employee_id': 'EMP001', 'role_id': 4, 'is_active': True, 'is_verified': True, 'last_login': None, 'created_at': '2025-07-10T10:00:00'},
]

# Configuration
class Config:
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    FASTAPI_BASE_URL = os.environ.get('FASTAPI_BASE_URL') or 'http://localhost:8000'
    WTF_CSRF_ENABLED = True

# Flask app initialization
app = Flask(__name__)
app.config.from_object(Config)

# API Client for FastAPI communication
class FastAPIClient:
    def __init__(self, base_url: str):
        self.base_url = base_url.rstrip('/')
        self.session = requests.Session()
    
    def set_auth_token(self, token: str):
        """Set JWT token for authenticated requests"""
        self.session.headers.update({'Authorization': f'Bearer {token}'})
    
    def clear_auth_token(self):
        """Clear JWT token"""
        if 'Authorization' in self.session.headers:
            del self.session.headers['Authorization']
    
    def login(self, username: str, password: str) -> Dict[str, Any]:
        """Authenticate with FastAPI backend"""
        try:
            response = self.session.post(
                f'{self.base_url}/auth/login',
                data={'username': username, 'password': password},
                headers={
                    'Content-Type': 'application/x-www-form-urlencoded',
                    'accept': 'application/json'
                }
            )
            if response.status_code == 200:
                return response.json()
            else:
                return {'error': 'Invalid credentials'}
        except Exception as e:
            return {'error': f'Connection error: {str(e)}'}
    
    def get(self, endpoint: str, params: Optional[Dict] = None) -> Dict[str, Any]:
        """Make GET request to FastAPI"""
        try:
            response = self.session.get(f'{self.base_url}{endpoint}', params=params)
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 401:
                return {'error': 'Unauthorized', 'status_code': 401}
            elif response.status_code == 403:
                return {'error': 'Forbidden - Admin access required', 'status_code': 403}
            else:
                return {'error': f'API Error: {response.status_code}', 'status_code': response.status_code}
        except Exception as e:
            return {'error': f'Connection error: {str(e)}'}
    
    def post(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Make POST request to FastAPI"""
        try:
            response = self.session.post(
                f'{self.base_url}{endpoint}',
                json=data,
                headers={'Content-Type': 'application/json'}
            )
            if response.status_code in [200, 201]:
                return response.json()
            elif response.status_code == 401:
                return {'error': 'Unauthorized', 'status_code': 401}
            elif response.status_code == 403:
                return {'error': 'Forbidden - Admin access required', 'status_code': 403}
            else:
                return {'error': f'API Error: {response.status_code}', 'status_code': response.status_code}
        except Exception as e:
            return {'error': f'Connection error: {str(e)}'}
    
    def put(self, endpoint: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """Make PUT request to FastAPI"""
        try:
            response = self.session.put(
                f'{self.base_url}{endpoint}',
                json=data,
                headers={'Content-Type': 'application/json'}
            )
            if response.status_code == 200:
                return response.json()
            elif response.status_code == 401:
                return {'error': 'Unauthorized', 'status_code': 401}
            elif response.status_code == 403:
                return {'error': 'Forbidden - Admin access required', 'status_code': 403}
            else:
                return {'error': f'API Error: {response.status_code}', 'status_code': response.status_code}
        except Exception as e:
            return {'error': f'Connection error: {str(e)}'}
    
    def delete(self, endpoint: str) -> Dict[str, Any]:
        """Make DELETE request to FastAPI"""
        try:
            response = self.session.delete(f'{self.base_url}{endpoint}')
            if response.status_code in [200, 204]:
                return {'success': True}
            elif response.status_code == 401:
                return {'error': 'Unauthorized', 'status_code': 401}
            elif response.status_code == 403:
                return {'error': 'Forbidden - Admin access required', 'status_code': 403}
            else:
                return {'error': f'API Error: {response.status_code}', 'status_code': response.status_code}
        except Exception as e:
            return {'error': f'Connection error: {str(e)}'}

# Global API client
api_client = FastAPIClient(Config.FASTAPI_BASE_URL)

# Authentication decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'access_token' not in session:
            return redirect(url_for('login', next=request.url))

        # Set token for the API client for this request
        api_client.set_auth_token(session['access_token'])

        # Ensure user is an admin
        if not session.get('user', {}).get('is_superuser'):
            flash('Admin access required.', 'danger')
            return redirect(url_for('login'))

        return f(*args, **kwargs)
    return decorated_function

def super_admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = session.get('user', {})
        if not user.get('is_superuser'):
            flash('Super Admin access required.', 'danger')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function


def app_admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        user = session.get('user', {})
        # Allow superusers to access app admin features
        if user.get('is_superuser'):
            return f(*args, **kwargs)
        if 'app_admin' not in session.get('roles', []):
            flash('Application Admin access required.', 'danger')
            return redirect(url_for('dashboard'))
        return f(*args, **kwargs)
    return decorated_function

# Forms
class LoginForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=3, max=50)])
    password = PasswordField('Password', validators=[DataRequired()])

class UserForm(FlaskForm):
    username = StringField('Username', validators=[DataRequired(), Length(min=3, max=50)])
    email = StringField('Email', validators=[DataRequired(), Email()])
    full_name = StringField('Full Name', validators=[OptionalValidator(), Length(max=255)])
    password = PasswordField('Password', validators=[OptionalValidator(), Length(min=6)])
    is_active = BooleanField('Active')
    is_verified = BooleanField('Verified')
    is_superuser = BooleanField('Admin')

class ApplicationForm(FlaskForm):
    name = StringField('Application Name', validators=[DataRequired(), Length(min=3, max=100)])
    description = TextAreaField('Description', validators=[OptionalValidator()])
    redirect_uris = TextAreaField('Redirect URIs (one per line)', validators=[DataRequired()])
    is_active = BooleanField('Active')

# Routes


@app.route('/permissions')
@login_required
def permissions():
    """List all permissions (static data)"""
    return render_template('permissions.html', permissions=PERMISSIONS)

@app.route('/permissions/create', methods=['GET', 'POST'])
@super_admin_required
def create_permission():
    class PermissionForm(FlaskForm):
        code = StringField('Permission Code', validators=[DataRequired(), Length(min=3, max=50)])
        label = StringField('Label', validators=[DataRequired(), Length(min=3, max=100)])
    form = PermissionForm()
    if form.validate_on_submit():
        if any(p['code'] == form.code.data for p in PERMISSIONS):
            flash('Permission code already exists!', 'error')
        else:
            PERMISSIONS.append({'code': form.code.data, 'label': form.label.data})
            flash('Permission created (static, not persisted)', 'success')
            return redirect(url_for('permissions'))
    return render_template('permission_form.html', form=form, title='Create Permission')

@app.route('/permissions/<code>/edit', methods=['GET', 'POST'])
@super_admin_required
def edit_permission(code):
    perm = next((p for p in PERMISSIONS if p['code'] == code), None)
    if not perm:
        flash('Permission not found', 'error')
        return redirect(url_for('permissions'))
    class PermissionForm(FlaskForm):
        code = StringField('Permission Code', validators=[DataRequired(), Length(min=3, max=50)])
        label = StringField('Label', validators=[DataRequired(), Length(min=3, max=100)])
    form = PermissionForm(obj=perm)
    if form.validate_on_submit():
        if any(p['code'] == form.code.data and p['code'] != code for p in PERMISSIONS):
            flash('Permission code already exists!', 'error')
        else:
            perm['code'] = form.code.data
            perm['label'] = form.label.data
            flash('Permission updated (static, not persisted)', 'success')
            return redirect(url_for('permissions'))
    return render_template('permission_form.html', form=form, title='Edit Permission')

@app.route('/permissions/<code>/delete', methods=['POST'])
@super_admin_required
def delete_permission(code):
    perm = next((p for p in PERMISSIONS if p['code'] == code), None)
    if perm:
        PERMISSIONS.remove(perm)
        flash('Permission deleted (static, not persisted)', 'success')
    else:
        flash('Permission not found', 'error')
    return redirect(url_for('permissions'))


@app.route('/roles')
@login_required
def roles():
    """List all roles (static data)"""
    permissions_dict = {p['code']: p['label'] for p in PERMISSIONS}
    permissions_desc = {p['code']: p.get('description', '') for p in PERMISSIONS}
    return render_template('roles.html', roles=ROLES, permissions_dict=permissions_dict, permissions_desc=permissions_desc)

@app.route('/roles/create', methods=['GET', 'POST'])
@super_admin_required
def create_role():
    class RoleForm(FlaskForm):
        name = StringField('Role Name', validators=[DataRequired(), Length(min=3, max=50)])
        permissions = SelectField('Permissions', choices=[(p['code'], p['label']) for p in PERMISSIONS], coerce=str, render_kw={'multiple': True})
    form = RoleForm()
    form.permissions.choices = [(p['code'], p['label']) for p in PERMISSIONS]
    if form.validate_on_submit():
        if any(r['name'].lower() == form.name.data.lower() for r in ROLES):
            flash('Role name already exists!', 'error')
        else:
            new_id = max(r['id'] for r in ROLES) + 1 if ROLES else 1
            ROLES.append({'id': new_id, 'name': form.name.data, 'permissions': request.form.getlist('permissions')})
            flash('Role created (static, not persisted)', 'success')
            return redirect(url_for('roles'))
    return render_template('role_form.html', form=form, title='Create Role', permissions=PERMISSIONS)

@app.route('/roles/<int:role_id>/edit', methods=['GET', 'POST'])
@super_admin_required
def edit_role(role_id):
    role = next((r for r in ROLES if r['id'] == role_id), None)
    if not role:
        flash('Role not found', 'error')
        return redirect(url_for('roles'))
    class RoleForm(FlaskForm):
        name = StringField('Role Name', validators=[DataRequired(), Length(min=3, max=50)])
        permissions = SelectField('Permissions', choices=[(p['code'], p['label']) for p in PERMISSIONS], coerce=str, render_kw={'multiple': True})
    form = RoleForm(obj=role)
    form.permissions.choices = [(p['code'], p['label']) for p in PERMISSIONS]
    form.permissions.data = role.get('permissions', [])
    if form.validate_on_submit():
        if any(r['name'].lower() == form.name.data.lower() and r['id'] != role_id for r in ROLES):
            flash('Role name already exists!', 'error')
        else:
            role['name'] = form.name.data
            role['permissions'] = request.form.getlist('permissions')
            flash('Role updated (static, not persisted)', 'success')
            return redirect(url_for('roles'))
    return render_template('role_form.html', form=form, title='Edit Role', permissions=PERMISSIONS, role=role)

@app.route('/roles/<int:role_id>/delete', methods=['POST'])
@super_admin_required
def delete_role(role_id):
    role = next((r for r in ROLES if r['id'] == role_id), None)
    if role:
        ROLES.remove(role)
        flash('Role deleted (static, not persisted)', 'success')
    else:
        flash('Role not found', 'error')
    return redirect(url_for('roles'))


@app.route('/me', methods=['GET', 'POST'])
@login_required
def me():
    """Profile page for the current user (static data)"""
    # For static demo, use session['user']['username'] if present, else default to 'alice'
    username = session.get('user', {}).get('username')
    user = next((u for u in USERS if u['username'] == username), None)
    if not user:
        user = USERS[0]  # fallback to first static user
    emp = next((e for e in EMPLOYEES if e['employee_id'] == user['employee_id']), None) if user else None
    if not user or not emp:
        flash('User not found', 'error')
        return redirect(url_for('users'))
    class StaticProfileForm(UserForm):
        employee_id = StringField('Employee ID', default=emp['employee_id'], render_kw={'readonly': True})
        branch_id = SelectField('Branch', choices=[(str(b['id']), b['name']) for b in BRANCHES], default=str(emp['branch_id']), render_kw={'disabled': True})
        position_id = SelectField('Position', choices=[(str(p['id']), p['name']) for p in POSITIONS], default=str(emp['position_id']), render_kw={'disabled': True})
        role_id = SelectField('Role', choices=[(str(r['id']), r['name']) for r in ROLES], default=str(user['role_id']), render_kw={'disabled': True})
        manager_id = SelectField('Manager', choices=[('', 'None')] + [(str(e['id']), e['full_name']) for e in EMPLOYEES], default=str(emp['manager_id']) if emp['manager_id'] else '', render_kw={'disabled': True})
    form = StaticProfileForm(obj=user)
    form.full_name.data = emp['full_name']
    if form.validate_on_submit():
        # Only allow updating username, email, full_name, password
        if any(u['username'] == form.username.data and u['id'] != user['id'] for u in USERS):
            flash('Username already exists!', 'error')
        else:
            user['username'] = form.username.data
            user['email'] = form.email.data
            emp['full_name'] = form.full_name.data
            if form.password.data:
                user['password'] = form.password.data
            flash('Profile updated (static, not persisted)', 'success')
            return redirect(url_for('me'))
    # Show effective permissions in profile
    role = next((r for r in ROLES if r['id'] == user['role_id']), None)
    permissions_dict = {p['code']: p['label'] for p in PERMISSIONS}
    effective_permissions = [permissions_dict.get(p, p) for p in (role['permissions'] if role else [])]
    return render_template('user_form.html', form=form, title='My Profile', user=user, profile_page=True, effective_permissions=effective_permissions)

@app.route('/')
def index():
    """Redirect to admin dashboard"""
    if 'access_token' in session:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    """Admin login page"""
    if 'access_token' in session and session.get('user', {}).get('is_superuser'):
        return redirect(url_for('dashboard'))

    form = LoginForm()
    if form.validate_on_submit():
        result = api_client.login(form.username.data, form.password.data)

        # For static demo, also check USERS
        user = next((u for u in USERS if u['username'] == form.username.data), None)
        if user and form.password.data == 'password':  # Static password for demo
            # Determine roles
            roles = []
            role = next((r for r in ROLES if r['id'] == user['role_id']), None)
            if role:
                if role['name'] == 'Application Admin':
                    roles.append('app_admin')
                if role['name'] == 'Admin':
                    user['is_superuser'] = True
                else:
                    user['is_superuser'] = False
            session['access_token'] = 'static-token'  # For demo
            session['user'] = user
            session['roles'] = roles
            api_client.set_auth_token('static-token')
            flash('Login successful! (static demo)', 'success')
            return redirect(url_for('dashboard'))
        elif 'access_token' in result and result.get('user', {}).get('is_superuser'):
            session['access_token'] = result['access_token']
            session['user'] = result['user']
            session['roles'] = result['user'].get('roles', [])
            api_client.set_auth_token(result['access_token'])
            flash('Login successful!', 'success')
            return redirect(url_for('dashboard'))
        elif 'access_token' in result:
            flash('You do not have admin privileges.', 'danger')
            return redirect(url_for('login'))
        else:
            flash(result.get('error', 'Invalid credentials or connection issue.'), 'danger')

    return render_template('login.html', form=form, title='Admin Login')

@app.route('/logout')
def logout():
    """Admin logout"""
    session.pop('access_token', None)
    session.pop('user', None)
    api_client.clear_auth_token()
    flash('You have been logged out.', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    """Admin dashboard"""
    stats = api_client.get('/api/admin/stats')

    if 'error' in stats:
        flash(f"Error loading dashboard: {stats['error']}", 'danger')
        if stats.get('status_code') in [401, 403]:
            return redirect(url_for('logout'))
        stats = {}

    return render_template('dashboard.html', stats=stats, user=session.get('user'))

# Apply super_admin_required to SSO Admin routes
@app.route('/users/create', methods=['GET', 'POST'])
@super_admin_required
def create_user():
    """Create a new user (static data, not persisted)"""
    class StaticUserForm(UserForm):
        employee_id = StringField('Employee ID', validators=[DataRequired(), Length(min=5, max=10)])
        branch_id = SelectField('Branch', choices=[(str(b['id']), b['name']) for b in BRANCHES])
        position_id = SelectField('Position', choices=[(str(p['id']), p['name']) for p in POSITIONS])
        role_id = SelectField('Role', choices=[(str(r['id']), r['name']) for r in ROLES])
        manager_id = SelectField('Manager', choices=[('', 'None')] + [(str(e['id']), e['full_name']) for e in EMPLOYEES])

    form = StaticUserForm()
    form.branch_id.choices = [(str(b['id']), b['name']) for b in BRANCHES]
    form.position_id.choices = [(str(p['id']), p['name']) for p in POSITIONS]
    form.role_id.choices = [(str(r['id']), r['name']) for r in ROLES]
    form.manager_id.choices = [('', 'None')] + [(str(e['id']), e['full_name']) for e in EMPLOYEES]

    if form.validate_on_submit():
        if any(u['username'] == form.username.data for u in USERS):
            flash('Username already exists!', 'error')
        elif any(e['employee_id'] == form.employee_id.data for e in EMPLOYEES):
            flash('Employee ID already exists!', 'error')
        elif not form.password.data:
            flash('Password is required for new users.', 'error')
        else:
            new_user_id = max([u['id'] for u in USERS] or [0]) + 1
            new_emp_id = max([e['id'] for e in EMPLOYEES] or [0]) + 1
            emp = {
                'id': new_emp_id,
                'employee_id': form.employee_id.data,
                'full_name': form.full_name.data,
                'branch_id': int(form.branch_id.data),
                'position_id': int(form.position_id.data),
                'manager_id': int(form.manager_id.data) if form.manager_id.data else None
            }
            EMPLOYEES.append(emp)
            user = {
                'id': new_user_id,
                'username': form.username.data,
                'email': form.email.data,
                'employee_id': form.employee_id.data,
                'role_id': int(form.role_id.data),
                'is_active': form.is_active.data,
                'is_verified': form.is_verified.data,
                'password': form.password.data,
                'last_login': None,
                'created_at': datetime.now().isoformat()
            }
            USERS.append(user)
            flash('User created (static, not persisted)', 'success')
            return redirect(url_for('users'))
    return render_template('user_form.html', form=form, title='Create User', user=None)

@app.route('/users', methods=['GET'])
@login_required
def users():
    """User management page with search and filter functionality"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '').lower()
    is_active = request.args.get('is_active')
    page_size = 20

    # Join user with employee, branch, position, department, role
    def enrich_user(user):
        emp = next((e for e in EMPLOYEES if e['employee_id'] == user['employee_id']), None)
        role = next((r for r in ROLES if r['id'] == user['role_id']), None)
        branch = next((b for b in BRANCHES if b['id'] == emp['branch_id']), None) if emp else None
        pos = next((p for p in POSITIONS if p['id'] == emp['position_id']), None) if emp else None
        dept = next((d for d in DEPARTMENTS if d['id'] == pos['department_id']), None) if pos else None
        manager = next((e for e in EMPLOYEES if emp and emp['manager_id'] == e['id']), None)
        
        # Get effective permissions for the role
        permissions_dict = {p['code']: p['label'] for p in PERMISSIONS}
        effective_permissions = [permissions_dict.get(p, p) for p in (role['permissions'] if role else [])]
        
        return {
            **user,
            'full_name': emp['full_name'] if emp else '',
            'branch': branch['name'] if branch else '',
            'position': pos['name'] if pos else '',
            'department': dept['name'] if dept else '',
            'manager_name': manager['full_name'] if manager else '',
            'role': role['name'] if role else '',
            'effective_permissions': effective_permissions,
        }

    # Filter users based on search and active status
    filtered = [enrich_user(u) for u in USERS if
        (not search or search in u['username'].lower() or search in u['email'].lower()) and
        (is_active is None or (str(u['is_active']).lower() == is_active.lower() if is_active else True))
    ]
    
    # Pagination
    total = len(filtered)
    pages = (total + page_size - 1) // page_size
    start = (page - 1) * page_size
    end = start + page_size
    paged = filtered[start:end]
    
    data = {
        'users': paged,
        'total': total,
        'page': page,
        'pages': pages,
    }
    
    return render_template(
        'users.html',
        data=data,
        search=search if search else '',
        is_active=is_active if is_active else '',
    )


@app.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@super_admin_required
def edit_user(user_id):
    """Edit user (static data)"""
    user = next((u for u in USERS if u['id'] == user_id), None)
    emp = next((e for e in EMPLOYEES if e['employee_id'] == user['employee_id']), None) if user else None
    if not user or not emp:
        flash('User not found', 'error')
        return redirect(url_for('users'))
    class StaticUserForm(UserForm):
        employee_id = StringField('Employee ID', default=emp['employee_id'], validators=[DataRequired(), Length(min=5, max=10)])
        branch_id = SelectField('Branch', choices=[(str(b['id']), b['name']) for b in BRANCHES], default=str(emp['branch_id']))
        position_id = SelectField('Position', choices=[(str(p['id']), p['name']) for p in POSITIONS], default=str(emp['position_id']))
        role_id = SelectField('Role', choices=[(str(r['id']), r['name']) for r in ROLES], default=str(user['role_id']))
        manager_id = SelectField('Manager', choices=[('', 'None')] + [(str(e['id']), e['full_name']) for e in EMPLOYEES], default=str(emp['manager_id']) if emp['manager_id'] else '')
    form = StaticUserForm(obj=user)
    if form.validate_on_submit():
        # Uniqueness checks (allow same user/emp)
        if any(u['username'] == form.username.data and u['id'] != user_id for u in USERS):
            flash('Username already exists!', 'error')
        elif any(e['employee_id'] == form.employee_id.data and e['id'] != emp['id'] for e in EMPLOYEES):
            flash('Employee ID already exists!', 'error')
        else:
            # Update EMPLOYEE
            emp['employee_id'] = form.employee_id.data
            emp['full_name'] = form.full_name.data
            emp['branch_id'] = int(form.branch_id.data)
            emp['position_id'] = int(form.position_id.data)
            emp['manager_id'] = int(form.manager_id.data) if form.manager_id.data else None
            # Update USER
            user['username'] = form.username.data
            user['email'] = form.email.data
            user['role_id'] = int(form.role_id.data)
            user['is_active'] = form.is_active.data
            user['is_verified'] = form.is_verified.data
            if form.password.data:
                user['password'] = form.password.data
            flash('User updated (static, not persisted)', 'success')
            return redirect(url_for('users'))
    # Show effective permissions in profile
    role = next((r for r in ROLES if r['id'] == user['role_id']), None)
    permissions_dict = {p['code']: p['label'] for p in PERMISSIONS}
    effective_permissions = [permissions_dict.get(p, p) for p in (role['permissions'] if role else [])]
    return render_template('user_form.html', form=form, title='Edit User', user=user, effective_permissions=effective_permissions)

    if user:
        USERS.remove(user)
        emp = next((e for e in EMPLOYEES if e['employee_id'] == user['employee_id']), None)
        if emp:
            EMPLOYEES.remove(emp)
        flash('User deleted (static, not persisted)', 'success')
    else:
        flash('User not found', 'error')
    return redirect(url_for('users'))





@app.route('/users/delete/<int:user_id>', methods=['POST'])
@super_admin_required
def delete_user(user_id):
    """Delete a user by user_id (static, not persisted)"""
    user = next((u for u in USERS if u['id'] == user_id), None)
    if user:
        USERS.remove(user)
        emp = next((e for e in EMPLOYEES if e['employee_id'] == user['employee_id']), None)
        if emp:
            EMPLOYEES.remove(emp)
        flash('User deleted (static, not persisted)', 'success')
    else:
        flash('User not found', 'error')
    return redirect(url_for('users'))




@app.route('/applications')
@super_admin_required
def applications():
    """Application management page"""
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    
    params = {'page': page, 'size': 20}
    if search:
        params['search'] = search
    
    apps_data = api_client.get('/api/admin/applications', params=params)
    
    if 'error' in apps_data:
        flash(f"Error loading applications: {apps_data['error']}", 'error')
        apps_data = {'applications': [], 'total': 0, 'page': 1, 'pages': 1}
    
    return render_template('applications.html', data=apps_data, search=search)

# Health check endpoint
@app.route('/health')
def health_check():
    """Health check endpoint for Docker"""
    return {'status': 'healthy', 'service': 'flask-admin'}, 200

# Error handlers
@app.errorhandler(404)
def not_found_error(error):
    return render_template('404.html'), 404

@app.errorhandler(500)
def internal_error(error):
    return render_template('500.html'), 500

# Template filters
@app.template_filter('datetime')
def datetime_filter(value):
    """Format datetime for display"""
    if value:
        try:
            if isinstance(value, str):
                dt = datetime.fromisoformat(value.replace('Z', '+00:00'))
            else:
                dt = value
            return dt.strftime('%Y-%m-%d %H:%M:%S')
        except:
            return value
    return 'Never'

@app.template_filter('status_badge')
def status_badge_filter(is_active):
    """Generate status badge HTML"""
    if is_active:
        return '<span class="badge bg-success">Active</span>'
    else:
        return '<span class="badge bg-danger">Inactive</span>'

# Application Admin: View users (read-only)
@app.route('/appadmin/users')
@app_admin_required
def appadmin_users():
    # Reuse enrich_user from users() route
    def enrich_user(user):
        emp = next((e for e in EMPLOYEES if e['employee_id'] == user['employee_id']), None)
        role = next((r for r in ROLES if r['id'] == user['role_id']), None)
        branch = next((b for b in BRANCHES if b['id'] == emp['branch_id']), None) if emp else None
        pos = next((p for p in POSITIONS if p['id'] == emp['position_id']), None) if emp else None
        dept = next((d for d in DEPARTMENTS if d['id'] == pos['department_id']), None) if pos else None
        manager = next((e for e in EMPLOYEES if emp and emp['manager_id'] == e['id']), None)
        permissions_dict = {p['code']: p['label'] for p in PERMISSIONS}
        effective_permissions = [permissions_dict.get(p, p) for p in (role['permissions'] if role else [])]
        return {
            **user,
            'full_name': emp['full_name'] if emp else '',
            'branch': branch['name'] if branch else '',
            'position': pos['name'] if pos else '',
            'department': dept['name'] if dept else '',
            'manager_name': manager['full_name'] if manager else '',
            'role': role['name'] if role else '',
            'effective_permissions': effective_permissions,
        }
    data = {'users': [enrich_user(u) for u in USERS]}
    return render_template('users.html', data=data, appadmin_readonly=True)

# Application Admin: View roles (read-only)
@app.route('/appadmin/roles')
@app_admin_required
def appadmin_roles():
    permissions_dict = {p['code']: p['label'] for p in PERMISSIONS}
    permissions_desc = {p['code']: p.get('description', '') for p in PERMISSIONS}
    return render_template('roles.html', roles=ROLES, permissions_dict=permissions_dict, permissions_desc=permissions_desc, appadmin_readonly=True)

# Application Admin: Assign role to user (POST)
@app.route('/appadmin/assign-role', methods=['POST'])
@app_admin_required
def appadmin_assign_role():
    user_id = request.form.get('user_id', type=int)
    role_id = request.form.get('role_id', type=int)
    user = next((u for u in USERS if u['id'] == user_id), None)
    role = next((r for r in ROLES if r['id'] == role_id), None)
    if not user or not role:
        flash('User or role not found.', 'danger')
        return redirect(url_for('appadmin_users'))
    user['role_id'] = role_id
    flash(f"Role '{role['name']}' assigned to user '{user['username']}' (static, not persisted)", 'success')
    return redirect(url_for('appadmin_users'))

if __name__ == '__main__':
    import argparse

    parser = argparse.ArgumentParser(description='Flask Admin Panel for SSO System')
    parser.add_argument('--port', type=int, default=5000, help='Port to run the application on')
    args = parser.parse_args()

    app.run(debug=True, host='0.0.0.0', port=args.port)