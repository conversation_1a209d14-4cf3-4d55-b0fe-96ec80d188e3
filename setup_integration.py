#!/usr/bin/env python3
"""
Setup Script for Flask Admin + FastAPI Integration

This script helps set up the integrated system by:
1. Creating necessary database tables
2. Creating an admin user
3. Setting up sample data
4. Verifying the integration
"""

import sys
import os
from pathlib import Path
import getpass
from datetime import datetime

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def setup_database():
    """Initialize database tables"""
    print("🗄️  Setting up database...")
    
    try:
        from app.core.database import create_tables
        create_tables()
        print("✅ Database tables created successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to create database tables: {e}")
        return False

def create_admin_user():
    """Create an admin user"""
    print("\n👤 Creating admin user...")
    
    try:
        from app.core.database import SessionLocal
        from app.services.user_service import UserService
        from app.schemas.user import UserCreate
        
        # Get admin user details
        print("Please provide admin user details:")
        username = input("Username: ").strip()
        if not username:
            username = "admin"
            print(f"Using default username: {username}")
        
        email = input("Email: ").strip()
        if not email:
            email = "<EMAIL>"
            print(f"Using default email: {email}")
        
        password = getpass.getpass("Password: ").strip()
        if not password:
            password = "admin123"
            print("Using default password: admin123")
        
        full_name = input("Full Name (optional): ").strip()
        if not full_name:
            full_name = "System Administrator"
        
        # Create user
        db = SessionLocal()
        try:
            user_service = UserService(db)
            
            # Check if user already exists
            existing_user = user_service.get_user_by_username(username)
            if existing_user:
                print(f"⚠️  User '{username}' already exists")
                
                # Update to make them superuser
                existing_user.is_superuser = True
                existing_user.is_active = True
                db.commit()
                print(f"✅ Updated '{username}' to be a superuser")
                return True
            
            # Create new admin user
            user_data = UserCreate(
                username=username,
                email=email,
                password=password,
                full_name=full_name
            )
            
            user = user_service.create_user(user_data)
            
            # Make them superuser
            user.is_superuser = True
            user.is_active = True
            user.is_verified = True
            db.commit()
            
            print(f"✅ Admin user '{username}' created successfully")
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Failed to create admin user: {e}")
        return False

def create_sample_data():
    """Create sample roles and permissions"""
    print("\n📝 Creating sample roles and permissions...")
    
    try:
        from app.core.database import SessionLocal
        from app.models.role import Role
        from app.models.permission import Permission
        
        db = SessionLocal()
        try:
            # Create permissions
            permissions_data = [
                ("view_users", "View Users", "Allows viewing user details and lists"),
                ("edit_users", "Edit Users", "Allows editing user information"),
                ("delete_users", "Delete Users", "Allows deletion of users"),
                ("manage_roles", "Manage Roles", "Allows creation and editing of roles"),
                ("view_applications", "View Applications", "Allows viewing OAuth applications"),
                ("edit_applications", "Edit Applications", "Allows editing OAuth applications"),
            ]
            
            created_permissions = []
            for perm_name, perm_label, perm_desc in permissions_data:
                existing_perm = db.query(Permission).filter(Permission.permission_name == perm_name).first()
                if not existing_perm:
                    permission = Permission(
                        permission_name=perm_name,
                        description=perm_desc
                    )
                    db.add(permission)
                    created_permissions.append(permission)
                else:
                    created_permissions.append(existing_perm)
            
            db.commit()
            
            # Create roles
            roles_data = [
                ("Admin", "System Administrator", ["view_users", "edit_users", "delete_users", "manage_roles", "view_applications", "edit_applications"]),
                ("Manager", "Branch Manager", ["view_users", "edit_users", "view_applications"]),
                ("Employee", "Regular Employee", ["view_users"]),
            ]
            
            for role_name, role_desc, role_perms in roles_data:
                existing_role = db.query(Role).filter(Role.role_name == role_name).first()
                if not existing_role:
                    role = Role(role_name=role_name, description=role_desc)
                    db.add(role)
                    db.flush()  # Get the ID
                    
                    # Add permissions
                    for perm_name in role_perms:
                        permission = db.query(Permission).filter(Permission.permission_name == perm_name).first()
                        if permission:
                            role.permissions.append(permission)
                    
                    db.commit()
                    print(f"✅ Created role: {role_name}")
                else:
                    print(f"⚠️  Role '{role_name}' already exists")
            
            print("✅ Sample data created successfully")
            return True
            
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Failed to create sample data: {e}")
        return False

def verify_setup():
    """Verify the setup is working"""
    print("\n🔍 Verifying setup...")
    
    try:
        from app.core.database import SessionLocal
        from app.models.user import User
        from app.models.role import Role
        from app.models.permission import Permission
        
        db = SessionLocal()
        try:
            # Check users
            user_count = db.query(User).count()
            admin_count = db.query(User).filter(User.is_superuser == True).count()
            
            # Check roles and permissions
            role_count = db.query(Role).count()
            permission_count = db.query(Permission).count()
            
            print(f"📊 Setup verification:")
            print(f"   - Users: {user_count} (Admins: {admin_count})")
            print(f"   - Roles: {role_count}")
            print(f"   - Permissions: {permission_count}")
            
            if admin_count > 0 and role_count > 0 and permission_count > 0:
                print("✅ Setup verification passed")
                return True
            else:
                print("❌ Setup verification failed")
                return False
                
        finally:
            db.close()
            
    except Exception as e:
        print(f"❌ Setup verification error: {e}")
        return False

def main():
    """Main setup function"""
    print("=" * 60)
    print("Flask Admin + FastAPI Backend Setup")
    print("=" * 60)
    
    steps = [
        ("Database Setup", setup_database),
        ("Admin User Creation", create_admin_user),
        ("Sample Data Creation", create_sample_data),
        ("Setup Verification", verify_setup)
    ]
    
    success_count = 0
    for step_name, step_func in steps:
        print(f"\n🚀 {step_name}...")
        if step_func():
            success_count += 1
        else:
            print(f"❌ {step_name} failed")
            break
    
    print("\n" + "=" * 60)
    if success_count == len(steps):
        print("🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Start FastAPI backend: uvicorn app.main:app --reload")
        print("2. Start Flask admin: python flask_admin/app.py")
        print("3. Run integration test: python test_integration.py")
        print("4. Access admin at: http://localhost:5000")
    else:
        print("❌ Setup completed with issues.")
        print("🔧 Please check the error messages above.")
    print("=" * 60)
    
    return 0 if success_count == len(steps) else 1

if __name__ == "__main__":
    exit(main())
