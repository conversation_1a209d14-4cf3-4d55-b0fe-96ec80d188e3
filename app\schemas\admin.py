from typing import List, Optional, Dict, Any
from pydantic import BaseModel, EmailStr
from datetime import datetime

class SystemStatsResponse(BaseModel):
    """System-wide statistics response"""
    total_users: int
    active_users: int
    verified_users: int
    recent_registrations: int  # Last 30 days
    total_applications: int
    active_applications: int
    recent_applications: int  # Last 30 days
    locked_users: int
    unverified_users: int

class UserStatsResponse(BaseModel):
    """Detailed user statistics response"""
    registration_trends: List[Dict[str, Any]]  # Monthly registration data
    top_active_users: List[Dict[str, Any]]  # Most active users

class AdminUserCreate(BaseModel):
    """Schema for admin user creation"""
    username: str
    email: EmailStr
    full_name: Optional[str] = None
    password: str
    is_active: bool = True
    is_verified: bool = False
    is_superuser: bool = False

class AdminUserUpdate(BaseModel):
    """Schema for admin user updates"""
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None
    is_verified: Optional[bool] = None
    is_superuser: Optional[bool] = None
    bio: Optional[str] = None
    timezone: Optional[str] = None
    language: Optional[str] = None

class AdminApplicationUpdate(BaseModel):
    """Schema for admin application updates"""
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    redirect_uris: Optional[List[str]] = None
    allowed_scopes: Optional[List[str]] = None
    grant_types: Optional[List[str]] = None
    response_types: Optional[List[str]] = None
    is_confidential: Optional[bool] = None
    require_consent: Optional[bool] = None
    website_url: Optional[str] = None
    privacy_policy_url: Optional[str] = None
    terms_of_service_url: Optional[str] = None
    logo_url: Optional[str] = None
    token_endpoint_auth_method: Optional[str] = None
    access_token_lifetime: Optional[int] = None
    refresh_token_lifetime: Optional[int] = None

class UserSearchResponse(BaseModel):
    """Response for user search"""
    users: List[Dict[str, Any]]
    total: int
    page: int
    limit: int

class ApplicationSearchResponse(BaseModel):
    """Response for application search"""
    applications: List[Dict[str, Any]]
    total: int
    page: int
    limit: int

class ActivityResponse(BaseModel):
    """Response for recent activities"""
    activities: List[Dict[str, Any]]

class AdminDashboardResponse(BaseModel):
    """Complete admin dashboard data"""
    system_stats: SystemStatsResponse
    user_stats: UserStatsResponse
    recent_activities: List[Dict[str, Any]]

class UserDetailResponse(BaseModel):
    """Detailed user information for admin"""
    id: str
    username: str
    email: str
    full_name: Optional[str]
    is_active: bool
    is_verified: bool
    is_superuser: bool
    failed_login_attempts: int
    locked_until: Optional[datetime]
    last_login: Optional[datetime]
    profile_picture: Optional[str]
    bio: Optional[str]
    timezone: str
    language: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

class ApplicationDetailResponse(BaseModel):
    """Detailed application information for admin"""
    id: str
    name: str
    description: Optional[str]
    client_id: str
    is_active: bool
    redirect_uris: List[str]
    allowed_scopes: List[str]
    grant_types: List[str]
    response_types: List[str]
    is_confidential: bool
    require_consent: bool
    website_url: Optional[str]
    privacy_policy_url: Optional[str]
    terms_of_service_url: Optional[str]
    logo_url: Optional[str]
    token_endpoint_auth_method: str
    access_token_lifetime: int
    refresh_token_lifetime: int
    created_at: datetime
    updated_at: datetime
    created_by: Optional[str]
    
    class Config:
        from_attributes = True

class BulkUserAction(BaseModel):
    """Schema for bulk user actions"""
    user_ids: List[str]
    action: str  # 'activate', 'deactivate', 'verify', 'delete', 'unlock'

class BulkApplicationAction(BaseModel):
    """Schema for bulk application actions"""
    application_ids: List[str]
    action: str  # 'activate', 'deactivate', 'delete'

class AdminActionResponse(BaseModel):
    """Response for admin actions"""
    success: bool
    message: str
    affected_count: Optional[int] = None
    errors: Optional[List[str]] = None