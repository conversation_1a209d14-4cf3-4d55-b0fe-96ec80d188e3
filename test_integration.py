#!/usr/bin/env python3
"""
Integration Test Script for Flask Admin + FastAPI Backend

This script tests the complete integration between the Flask admin frontend
and the FastAPI backend to ensure all endpoints work correctly.
"""

import requests
import json
import time
from typing import Dict, Any

class IntegrationTester:
    def __init__(self, fastapi_url: str = "http://localhost:8000", flask_url: str = "http://localhost:5000"):
        self.fastapi_url = fastapi_url.rstrip('/')
        self.flask_url = flask_url.rstrip('/')
        self.session = requests.Session()
        self.access_token = None
        
    def log(self, message: str, level: str = "INFO"):
        """Log test messages"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {level}: {message}")
    
    def test_fastapi_health(self) -> bool:
        """Test FastAPI health endpoint"""
        try:
            response = self.session.get(f"{self.fastapi_url}/health")
            if response.status_code == 200:
                self.log("✅ FastAPI health check passed")
                return True
            else:
                self.log(f"❌ FastAPI health check failed: {response.status_code}", "ERROR")
                return False
        except Exception as e:
            self.log(f"❌ FastAPI connection failed: {e}", "ERROR")
            return False
    
    def test_flask_health(self) -> bool:
        """Test Flask admin health endpoint"""
        try:
            response = self.session.get(f"{self.flask_url}/health")
            if response.status_code == 200:
                self.log("✅ Flask admin health check passed")
                return True
            else:
                self.log(f"❌ Flask admin health check failed: {response.status_code}", "ERROR")
                return False
        except Exception as e:
            self.log(f"❌ Flask admin connection failed: {e}", "ERROR")
            return False
    
    def create_test_admin_user(self) -> bool:
        """Create a test admin user via FastAPI"""
        try:
            # First check if admin user already exists
            login_data = {
                'username': 'testadmin',
                'password': 'testpassword123'
            }
            
            response = self.session.post(
                f"{self.fastapi_url}/auth/login",
                data=login_data,
                headers={'accept': 'application/json'}
            )
            
            if response.status_code == 200:
                self.log("✅ Test admin user already exists")
                return True
            
            # Create admin user via database initialization
            self.log("ℹ️  Test admin user doesn't exist, you may need to create one manually")
            self.log("ℹ️  Run: python migrations/init_db.py and create an admin user")
            return False
            
        except Exception as e:
            self.log(f"❌ Failed to create test admin user: {e}", "ERROR")
            return False
    
    def test_authentication(self) -> bool:
        """Test authentication flow"""
        try:
            login_data = {
                'username': 'testadmin',
                'password': 'testpassword123'
            }
            
            response = self.session.post(
                f"{self.fastapi_url}/auth/login",
                data=login_data,
                headers={'accept': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if 'access_token' in result:
                    self.access_token = result['access_token']
                    self.session.headers.update({'Authorization': f'Bearer {self.access_token}'})
                    self.log("✅ Authentication successful")
                    return True
                else:
                    self.log("❌ No access token in response", "ERROR")
                    return False
            else:
                self.log(f"❌ Authentication failed: {response.status_code}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Authentication error: {e}", "ERROR")
            return False
    
    def test_admin_endpoints(self) -> bool:
        """Test admin API endpoints"""
        if not self.access_token:
            self.log("❌ No access token available for admin tests", "ERROR")
            return False
        
        endpoints_to_test = [
            ("/api/admin/stats", "Admin statistics"),
            ("/api/admin/users/search", "User search"),
            ("/api/admin/roles", "Roles list"),
            ("/api/admin/permissions", "Permissions list"),
            ("/api/admin/applications", "Applications list")
        ]
        
        all_passed = True
        for endpoint, description in endpoints_to_test:
            try:
                response = self.session.get(f"{self.fastapi_url}{endpoint}")
                if response.status_code == 200:
                    self.log(f"✅ {description} endpoint working")
                else:
                    self.log(f"❌ {description} endpoint failed: {response.status_code}", "ERROR")
                    all_passed = False
            except Exception as e:
                self.log(f"❌ {description} endpoint error: {e}", "ERROR")
                all_passed = False
        
        return all_passed
    
    def test_flask_admin_pages(self) -> bool:
        """Test Flask admin pages"""
        # Note: This would require session handling for Flask
        # For now, just test that pages are accessible
        pages_to_test = [
            ("/login", "Login page"),
            ("/health", "Health check")
        ]
        
        all_passed = True
        for page, description in pages_to_test:
            try:
                response = self.session.get(f"{self.flask_url}{page}")
                if response.status_code in [200, 302]:  # 302 for redirects
                    self.log(f"✅ {description} accessible")
                else:
                    self.log(f"❌ {description} failed: {response.status_code}", "ERROR")
                    all_passed = False
            except Exception as e:
                self.log(f"❌ {description} error: {e}", "ERROR")
                all_passed = False
        
        return all_passed
    
    def run_all_tests(self) -> bool:
        """Run all integration tests"""
        self.log("🚀 Starting integration tests...")
        
        tests = [
            ("FastAPI Health", self.test_fastapi_health),
            ("Flask Health", self.test_flask_health),
            ("Create Test User", self.create_test_admin_user),
            ("Authentication", self.test_authentication),
            ("Admin Endpoints", self.test_admin_endpoints),
            ("Flask Admin Pages", self.test_flask_admin_pages)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            self.log(f"🧪 Running {test_name} test...")
            if test_func():
                passed += 1
            else:
                self.log(f"❌ {test_name} test failed", "ERROR")
        
        self.log(f"📊 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            self.log("🎉 All integration tests passed!")
            return True
        else:
            self.log("⚠️  Some tests failed. Check the logs above.", "WARNING")
            return False

def main():
    """Main test function"""
    print("=" * 60)
    print("Flask Admin + FastAPI Backend Integration Test")
    print("=" * 60)
    
    tester = IntegrationTester()
    success = tester.run_all_tests()
    
    print("=" * 60)
    if success:
        print("✅ Integration test completed successfully!")
        print("🚀 Your Flask admin frontend is ready to use with FastAPI backend!")
    else:
        print("❌ Integration test completed with issues.")
        print("🔧 Please check the error messages above and fix the issues.")
    print("=" * 60)
    
    return 0 if success else 1

if __name__ == "__main__":
    exit(main())
