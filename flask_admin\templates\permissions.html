{% extends "base.html" %}

{% block title %}Permissions - SSO Admin Panel{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-key"></i> Permissions</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="{{ url_for('create_permission') }}" class="btn btn-primary">
                <i class="bi bi-plus-circle"></i> New Permission
            </a>
        </div>
    </div>
</div>

<div class="table-responsive">
    <table class="table table-striped align-middle">
        <thead>
            <tr>
                <th>Code</th>
                <th>Label</th>
                <th width="120">Actions</th>
            </tr>
        </thead>
        <tbody>
            {% for perm in permissions %}
            <tr>
                <td>{{ perm.code }}</td>
                <td>
    <span data-bs-toggle="tooltip" data-bs-placement="top" title="{{ perm.description }}">
        {{ perm.label }}
    </span>
</td>
                <td>
                    <div class="btn-group btn-group-sm" role="group">
                        <a href="{{ url_for('edit_permission', code=perm.code) }}" class="btn btn-outline-primary" title="Edit">
                            <i class="bi bi-pencil"></i>
                        </a>
                        <!-- Delete Button triggers Modal -->
<button type="button" class="btn btn-outline-danger" title="Delete" data-bs-toggle="modal" data-bs-target="#deletePermissionModal{{ perm.code }}">
    <i class="bi bi-trash"></i>
</button>
<!-- Modal -->
<div class="modal fade" id="deletePermissionModal{{ perm.code }}" tabindex="-1" aria-labelledby="deletePermissionModalLabel{{ perm.code }}" aria-hidden="true">
  <div class="modal-dialog modal-dialog-centered">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="deletePermissionModalLabel{{ perm.code }}">Confirm Delete</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        Are you sure you want to delete the permission <strong>{{ perm.label }}</strong>?
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <form method="POST" action="{{ url_for('delete_permission', code=perm.code) }}" class="d-inline m-0 p-0">
          <button type="submit" class="btn btn-danger">Delete</button>
        </form>
      </div>
    </div>
  </div>
</div>
                    </div>
                </td>
            </tr>
            {% else %}
            <tr><td colspan="3" class="text-center text-muted">No permissions found.</td></tr>
            {% endfor %}
        </tbody>
    </table>
</div>
{% endblock %}

{% block scripts %}
<script>
    document.addEventListener('DOMContentLoaded', function () {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.forEach(function (tooltipTriggerEl) {
            new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
{% endblock %}
