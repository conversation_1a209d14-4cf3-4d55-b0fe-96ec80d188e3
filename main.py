import os
import uuid
import json
from datetime import datetime, timed<PERSON>ta
from typing import Optional, List
from fastapi import FastAPI, Request, Depends, Form, HTTPException, status, Response
from fastapi.responses import RedirectResponse, HTMLResponse, JSONResponse
from fastapi.templating import Jin<PERSON>2Templates
from fastapi.security import OA<PERSON>2<PERSON><PERSON>wordBearer
from sqlalchemy import create_engine, Column, String, Boolean, DateTime, ForeignKey, JSON as SAJSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, relationship
from passlib.context import CryptContext
from jose import jwt
import redis

DATABASE_URL = os.getenv("DATABASE_URL", "postgresql://sso_user:sso_pass@localhost:5432/sso")
CACHE_URL = os.getenv("CACHE_URL", "redis://localhost:6379")
JWT_ALGORITHM = os.getenv("JWT_ALGORITHM", "RS256")
JWT_PRIVATE_KEY = os.getenv("JWT_PRIVATE_KEY", "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----")
JWT_PUBLIC_KEY = os.getenv("JWT_PUBLIC_KEY", "-----BEGIN PUBLIC KEY-----\n...\n-----END PUBLIC KEY-----")
JWT_EXP_MIN = 15

Base = declarative_base()
engine = create_engine(DATABASE_URL)
SessionLocal = sessionmaker(bind=engine)
templates = Jinja2Templates(directory="templates")
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")
r = redis.Redis.from_url(CACHE_URL, decode_responses=True)

class User(Base):
    __tablename__ = "users"
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    username = Column(String, unique=True, nullable=False)
    email = Column(String)
    hashed_password = Column(String, nullable=False)
    full_name = Column(String)
    company = Column(String)
    department = Column(String)
    position = Column(String)
    employee_id = Column(String)
    phone = Column(String)
    manager_id = Column(String, ForeignKey("users.id"))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)
    manager = relationship("User", remote_side=[id])

class Application(Base):
    __tablename__ = "applications"
    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    name = Column(String, nullable=False)
    client_id = Column(String, unique=True, nullable=False)
    client_secret_hash = Column(String, nullable=False)
    redirect_uris = Column(SAJSON, nullable=False)
    owner_id = Column(String, ForeignKey("users.id"))
    created_at = Column(DateTime, default=datetime.utcnow)

def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

def hash_password(password: str) -> str:
    return pwd_context.hash(password)

def verify_password(password: str, hashed: str) -> bool:
    return pwd_context.verify(password, hashed)

def create_jwt(payload: dict, exp_min=JWT_EXP_MIN) -> str:
    to_encode = payload.copy()
    to_encode["exp"] = datetime.utcnow() + timedelta(minutes=exp_min)
    return jwt.encode(to_encode, JWT_PRIVATE_KEY, algorithm=JWT_ALGORITHM)

def decode_jwt(token: str) -> dict:
    return jwt.decode(token, JWT_PUBLIC_KEY, algorithms=[JWT_ALGORITHM])

def create_session(user_id: str) -> str:
    sid = str(uuid.uuid4())
    r.setex(f"session:{sid}", 86400, user_id)
    return sid

def get_session(sid: str) -> Optional[str]:
    return r.get(f"session:{sid}")

def create_auth_code(user_id: str, client_id: str, redirect_uri: str) -> str:
    code = str(uuid.uuid4())
    r.setex(f"auth_code:{code}", 600, json.dumps({"user_id": user_id, "client_id": client_id, "redirect_uri": redirect_uri}))
    return code

def consume_auth_code(code: str) -> Optional[dict]:
    data = r.get(f"auth_code:{code}")
    if data:
        r.delete(f"auth_code:{code}")
        return json.loads(data)
    return None

app = FastAPI()

@app.post("/register")
async def register(
    username: str = Form(...),
    password: str = Form(...),
    email: str = Form(None),
    full_name: str = Form(None),
    db=Depends(get_db)
):
    if db.query(User).filter_by(username=username).first():
        raise HTTPException(status_code=400, detail="Username exists")
    user = User(
        username=username,
        hashed_password=hash_password(password),
        email=email,
        full_name=full_name,
        created_at=datetime.utcnow(),
        updated_at=datetime.utcnow(),
    )
    db.add(user)
    db.commit()
    return {"msg": "registered"}

@app.post("/login")
async def login(
    username: str = Form(...),
    password: str = Form(...),
    db=Depends(get_db)
):
    user = db.query(User).filter_by(username=username).first()
    if not user or not verify_password(password, user.hashed_password):
        raise HTTPException(status_code=401, detail="Invalid credentials")
    sid = create_session(user.id)
    resp = RedirectResponse(url="/", status_code=302)
    resp.set_cookie("session", sid, httponly=True)
    return resp

@app.get("/logout")
async def logout(request: Request):
    sid = request.cookies.get("session")
    if sid:
        r.delete(f"session:{sid}")
    resp = RedirectResponse(url="/", status_code=302)
    resp.delete_cookie("session")
    return resp

@app.post("/applications")
async def register_app(
    name: str = Form(...),
    redirect_uris: str = Form(...),
    client_id: str = Form(...),
    client_secret: str = Form(...),
    db=Depends(get_db),
    request: Request = None
):
    owner_id = None
    sid = request.cookies.get("session") if request else None
    if sid:
        owner_id = get_session(sid)
    if not owner_id:
        raise HTTPException(status_code=401, detail="Not authenticated")
    if db.query(Application).filter_by(client_id=client_id).first():
        raise HTTPException(status_code=400, detail="Client ID exists")
    app_obj = Application(
        name=name,
        client_id=client_id,
        client_secret_hash=hash_password(client_secret),
        redirect_uris=json.loads(redirect_uris),
        owner_id=owner_id,
        created_at=datetime.utcnow(),
    )
    db.add(app_obj)
    db.commit()
    return {"msg": "application registered"}

@app.get("/authorize")
async def authorize(
    response_type: str,
    client_id: str,
    redirect_uri: str,
    scope: str = "",
    state: str = "",
    request: Request,
    db=Depends(get_db)
):
    sid = request.cookies.get("session")
    user_id = get_session(sid) if sid else None
    app_obj = db.query(Application).filter_by(client_id=client_id).first()
    if not app_obj or redirect_uri not in app_obj.redirect_uris:
        raise HTTPException(status_code=400, detail="Invalid client or redirect_uri")
    if not user_id:
        return templates.TemplateResponse("login.html", {"request": request, "client_id": client_id, "redirect_uri": redirect_uri, "state": state})
    # Consent page could be shown here, but for minimal version, auto-consent
    code = create_auth_code(user_id, client_id, redirect_uri)
    url = f"{redirect_uri}?code={code}&state={state}"
    return RedirectResponse(url=url, status_code=302)

@app.post("/token")
async def token(
    grant_type: str = Form(...),
    code: str = Form(...),
    client_id: str = Form(...),
    client_secret: str = Form(...),
    db=Depends(get_db)
):
    if grant_type != "authorization_code":
        raise HTTPException(status_code=400, detail="Unsupported grant_type")
    app_obj = db.query(Application).filter_by(client_id=client_id).first()
    if not app_obj or not verify_password(client_secret, app_obj.client_secret_hash):
        raise HTTPException(status_code=401, detail="Invalid client credentials")
    code_data = consume_auth_code(code)
    if not code_data or code_data["client_id"] != client_id:
        raise HTTPException(status_code=400, detail="Invalid code")
    user = db.query(User).filter_by(id=code_data["user_id"]).first()
    if not user:
        raise HTTPException(status_code=400, detail="User not found")
    access_token = create_jwt({"sub": user.id, "scope": "openid profile email"})
    id_token = create_jwt({"sub": user.id, "email": user.email, "name": user.full_name})
    return {
        "access_token": access_token,
        "id_token": id_token,
        "token_type": "Bearer",
        "expires_in": JWT_EXP_MIN * 60,
    }

@app.get("/userinfo")
async def userinfo(request: Request, db=Depends(get_db)):
    auth = request.headers.get("authorization")
    if not auth or not auth.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Missing token")
    token = auth.split(" ")[1]
    try:
        payload = decode_jwt(token)
    except Exception:
        raise HTTPException(status_code=401, detail="Invalid token")
    user = db.query(User).filter_by(id=payload["sub"]).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")
    return {
        "sub": user.id,
        "username": user.username,
        "email": user.email,
        "full_name": user.full_name,
        "company": user.company,
        "department": user.department,
        "position": user.position,
        "employee_id": user.employee_id,
        "phone": user.phone,
        "manager_id": user.manager_id,
        "is_active": user.is_active,
        "created_at": user.created_at.isoformat(),
        "updated_at": user.updated_at.isoformat(),
    }

@app.get("/.well-known/openid-configuration")
async def oidc_config(request: Request):
    host = request.url.scheme + "://" + request.url.hostname
    return {
        "issuer": host,
        "authorization_endpoint": host + "/authorize",
        "token_endpoint": host + "/token",
        "userinfo_endpoint": host + "/userinfo",
        "jwks_uri": host + "/.well-known/jwks.json",
        "response_types_supported": ["code"],
        "subject_types_supported": ["public"],
        "id_token_signing_alg_values_supported": [JWT_ALGORITHM],
        "scopes_supported": ["openid", "profile", "email"],
        "token_endpoint_auth_methods_supported": ["client_secret_post"],
    }