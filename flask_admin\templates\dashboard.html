{% extends "base.html" %} {% block title %}Dashboard - SSO Admin Panel{%
endblock %} {% block content %}
<div
  class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom"
>
  <h1 class="h2"><i class="bi bi-speedometer2"></i> Admin Dashboard</h1>
  <div class="btn-toolbar mb-2 mb-md-0">
    <div class="btn-group me-2">
      <button
        type="button"
        class="btn btn-sm btn-outline-secondary"
        onclick="location.reload()"
      >
        <i class="bi bi-arrow-clockwise"></i> Refresh
      </button>
    </div>
  </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
  <div class="col-xl-3 col-md-6 mb-4">
    <div class="card stat-card">
      <div class="card-body">
        <div class="row no-gutters align-items-center">
          <div class="col mr-2">
            <div class="text-xs font-weight-bold text-uppercase mb-1">
              Total Users
            </div>
            <div class="h5 mb-0 font-weight-bold">
              {{ stats.system_stats.total_users if stats.system_stats else 0 }}
            </div>
          </div>
          <div class="col-auto">
            <i class="bi bi-people" style="font-size: 2rem; opacity: 0.3"></i>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-xl-3 col-md-6 mb-4">
    <div class="card stat-card-success">
      <div class="card-body">
        <div class="row no-gutters align-items-center">
          <div class="col mr-2">
            <div class="text-xs font-weight-bold text-uppercase mb-1">
              Active Users
            </div>
            <div class="h5 mb-0 font-weight-bold">
              {{ stats.system_stats.active_users if stats.system_stats else 0 }}
            </div>
          </div>
          <div class="col-auto">
            <i
              class="bi bi-person-check"
              style="font-size: 2rem; opacity: 0.3"
            ></i>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-xl-3 col-md-6 mb-4">
    <div class="card stat-card-info">
      <div class="card-body">
        <div class="row no-gutters align-items-center">
          <div class="col mr-2">
            <div class="text-xs font-weight-bold text-uppercase mb-1">
              Applications
            </div>
            <div class="h5 mb-0 font-weight-bold">
              {{ stats.system_stats.total_applications if stats.system_stats else 0 }}
            </div>
          </div>
          <div class="col-auto">
            <i class="bi bi-app" style="font-size: 2rem; opacity: 0.3"></i>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="col-xl-3 col-md-6 mb-4">
    <div class="card stat-card-warning">
      <div class="card-body">
        <div class="row no-gutters align-items-center">
          <div class="col mr-2">
            <div class="text-xs font-weight-bold text-uppercase mb-1">
              Failed Logins (24h)
            </div>
            <div class="h5 mb-0 font-weight-bold">
              {{ stats.system_stats.failed_logins_24h if stats.system_stats else 0 }}
            </div>
          </div>
          <div class="col-auto">
            <i
              class="bi bi-shield-exclamation"
              style="font-size: 2rem; opacity: 0.3"
            ></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Recent Activity and Quick Actions -->
<div class="row">
  <!-- Recent Activities -->
  <div class="col-lg-8 mb-4">
    <div class="card">
      <div
        class="card-header d-flex justify-content-between align-items-center"
      >
        <h5 class="mb-0">
          <i class="bi bi-clock-history"></i> Recent Activities
        </h5>
        <a href="#" class="btn btn-sm btn-outline-primary">View All</a>
      </div>
      <div class="card-body">
        {% if stats.recent_activities %}
        <div class="list-group list-group-flush">
          {% for activity in stats.recent_activities[:10] %}
          <div
            class="list-group-item d-flex justify-content-between align-items-start"
          >
            <div class="ms-2 me-auto">
              <div class="fw-bold">
                {% if activity.type == 'user_registration' %}
                <i class="bi bi-person-plus text-success"></i> New User
                Registration {% elif activity.type == 'application_created' %}
                <i class="bi bi-app text-info"></i> New Application {% elif
                activity.type == 'user_login' %}
                <i class="bi bi-box-arrow-in-right text-primary"></i> User Login
                {% else %}
                <i class="bi bi-activity text-secondary"></i> Activity {% endif
                %}
              </div>
              <small class="text-muted">{{ activity.description }}</small>
            </div>
            <small class="text-muted"
              >{{ activity.created_at | datetime }}</small
            >
          </div>
          {% endfor %}
        </div>
        {% else %}
        <div class="text-center py-4">
          <i class="bi bi-inbox" style="font-size: 3rem; color: #dee2e6"></i>
          <p class="text-muted mt-2">No recent activities</p>
        </div>
        {% endif %}
      </div>
    </div>
  </div>

  <!-- Quick Actions -->
  <div class="col-lg-4 mb-4">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-lightning"></i> Quick Actions</h5>
      </div>
      <div class="card-body">
        <div class="d-grid gap-2">
          <a href="{{ url_for('create_user') }}" class="btn btn-primary">
            <i class="bi bi-person-plus"></i> Create New User
          </a>
          <a href="{{ url_for('users') }}" class="btn btn-outline-primary">
            <i class="bi bi-people"></i> Manage Users
          </a>
          <a
            href="{{ url_for('applications') }}"
            class="btn btn-outline-primary"
          >
            <i class="bi bi-app"></i> Manage Applications
          </a>
          <button class="btn btn-outline-secondary" onclick="generateReport()">
            <i class="bi bi-file-earmark-text"></i> Generate Report
          </button>
        </div>
      </div>
    </div>

    <!-- System Status -->
    <div class="card mt-4">
      <div class="card-header">
        <h5 class="mb-0"><i class="bi bi-cpu"></i> System Status</h5>
      </div>
      <div class="card-body">
        <div class="row text-center">
          <div class="col-6">
            <div class="border-end">
              <div class="h4 text-success">
                <i class="bi bi-check-circle"></i>
              </div>
              <small class="text-muted">API Status</small>
            </div>
          </div>
          <div class="col-6">
            <div class="h4 text-success"><i class="bi bi-database"></i></div>
            <small class="text-muted">Database</small>
          </div>
        </div>
        <hr />
        <div class="text-center">
          <small class="text-muted">
            <i class="bi bi-clock"></i> Last updated: {{
            moment().format('YYYY-MM-DD HH:mm:ss') if moment else 'Just now' }}
          </small>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- User Statistics Chart -->
{% if stats.user_stats %}
<div class="row">
  <div class="col-12">
    <div class="card">
      <div class="card-header">
        <h5 class="mb-0">
          <i class="bi bi-graph-up"></i> User Registration Trends
        </h5>
      </div>
      <div class="card-body">
        <canvas
          id="userStatsChart"
          width="400"
          height="100"
          data-labels='{{ (stats.user_stats.registration_dates or []) | tojson | safe }}'
          data-counts='{{ (stats.user_stats.registration_counts or []) | tojson | safe }}'
        ></canvas>
      </div>
    </div>
  </div>
</div>
{% endif %} {% endblock %} {% block scripts %} {% if stats.user_stats %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  // User Registration Chart
  const canvas = document.getElementById('userStatsChart');
  if (canvas) {
    const ctx = canvas.getContext('2d');
    try {
      const labels = JSON.parse(canvas.dataset.labels || '[]');
      const counts = JSON.parse(canvas.dataset.counts || '[]');

      const userStatsChart = new Chart(ctx, {
        type: 'line',
        data: {
          labels: labels,
          datasets: [
            {
              label: 'New Registrations',
              data: counts,
              borderColor: 'rgb(102, 126, 234)',
              backgroundColor: 'rgba(102, 126, 234, 0.1)',
              tension: 0.4,
              fill: true,
            },
          ],
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              display: false,
            },
          },
          scales: {
            y: {
              beginAtZero: true,
              ticks: {
                stepSize: 1,
              },
            },
          },
        },
      });
    } catch (e) {
      console.error('Failed to parse chart data:', e);
      const ctx = canvas.getContext('2d');
      ctx.font = '16px Arial';
      ctx.fillStyle = 'red';
      ctx.textAlign = 'center';
      ctx.fillText('Error: Could not load chart data.', canvas.width / 2, canvas.height / 2);
    }
  }
</script>
{% endif %}

<script>
  function generateReport() {
    // Placeholder for report generation
    alert('Report generation feature coming soon!');
  }

  // Auto-refresh dashboard every 5 minutes
  setTimeout(function () {
    location.reload();
  }, 300000);
</script>
{% endblock %}
